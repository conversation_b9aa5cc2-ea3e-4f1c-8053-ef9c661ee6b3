#!/bin/bash

# ---- CONFIG ----
SOURCE_FILE="source.txt"  # File containing URLs and timing info
TEMP_DIR="temp_download"
FINAL_OUTPUT="final_concatenated.mp4"

# Frame rate enhancement options:
# "30" = Simple 24fps -> 30fps (fast, good quality)
# "60" = 24fps -> 60fps using FFmpeg interpolation (slower, decent quality)
# "60_rife" = 24fps -> 60fps using RIFE AI (slowest, best quality)
TARGET_FPS="30"  # Change this to "60" or "60_rife" for higher frame rates
# ----------------

# ---- GPU Acceleration Setup ----
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:2048,expandable_segments:True
# ---------------------------------

# ---- Timing Variables ----
VIDEO_START=""
TRIM_START=""
TRIM_END=""
TRIM_START_SECONDS=0
FINAL_DURATION_SECONDS=0
# --------------------------

set -e  # Exit on error
set -o pipefail  # Exit on pipe failures

# ---- Time conversion functions ----
# Convert time format (HH:MM:SSPM/AM or HH:MM:SS) to seconds since midnight
time_to_seconds() {
  local time_str="$1"

  # Handle AM/PM format (e.g., "10:07:43PM")
  if [[ "$time_str" =~ ([0-9]{1,2}):([0-9]{2}):([0-9]{2})(AM|PM) ]]; then
    local hour=${BASH_REMATCH[1]}
    local minute=${BASH_REMATCH[2]}
    local second=${BASH_REMATCH[3]}
    local ampm=${BASH_REMATCH[4]}

    # Convert to 24-hour format
    if [[ "$ampm" == "PM" && "$hour" != "12" ]]; then
      hour=$((hour + 12))
    elif [[ "$ampm" == "AM" && "$hour" == "12" ]]; then
      hour=0
    fi

    echo $((hour * 3600 + minute * 60 + second))
  # Handle 24-hour format (e.g., "22:07:43")
  elif [[ "$time_str" =~ ([0-9]{1,2}):([0-9]{2}):([0-9]{2}) ]]; then
    local hour=${BASH_REMATCH[1]}
    local minute=${BASH_REMATCH[2]}
    local second=${BASH_REMATCH[3]}

    echo $((hour * 3600 + minute * 60 + second))
  else
    echo "❌ ERROR: Invalid time format: $time_str" >&2
    echo "   Expected format: HH:MM:SSAM/PM or HH:MM:SS" >&2
    exit 1
  fi
}

# Convert seconds to HH:MM:SS format
seconds_to_time() {
  local total_seconds="$1"
  local hours=$((total_seconds / 3600))
  local minutes=$(((total_seconds % 3600) / 60))
  local seconds=$((total_seconds % 60))
  printf "%02d:%02d:%02d" "$hours" "$minutes" "$seconds"
}
# ---------------------------------------

# ---- Command line argument parsing ----
RESUME_MODE=0
if [ "$1" = "--resume" ]; then
  RESUME_MODE=1
  echo "🔄 Resume mode enabled: Will skip completed steps and resume from last incomplete step."
fi

if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  echo "Usage: $0 [OPTIONS]"
  echo ""
  echo "Description:"
  echo "  Downloads TS files from m3u8 URLs, concatenates them, and trims to specified times"
  echo ""
  echo "Options:"
  echo "  --help, -h               Show this help message"
  echo ""
  echo "Configuration:"
  echo "  Edit the script to modify output filename, etc."
  echo ""
  echo "Source file format ($SOURCE_FILE):"
  echo "  video_start=10:00:00PM"
  echo "  trim_start=10:07:43PM"
  echo "  trim_end=11:15:00PM"
  echo "  https://example.com/stream1.m3u8"
  echo "  https://example.com/stream2.m3u8"
  echo "  # Comments start with #"
  echo ""
  echo "Time formats supported:"
  echo "  - 12-hour: 10:07:43PM, 11:15:00AM"
  echo "  - 24-hour: 22:07:43, 11:15:00"
  exit 0
fi

# ---- Check source file ----
if [ ! -f "$SOURCE_FILE" ]; then
  echo "❌ ERROR: Source file does not exist: $SOURCE_FILE"
  echo "Please create $SOURCE_FILE with one m3u8 URL per line."
  exit 1
fi

echo "✅ Source file found. Continuing..."

# ---- Check GPU capabilities ----
echo "🔍 Checking NVIDIA GPU capabilities..."
if command -v nvidia-smi >/dev/null 2>&1; then
  gpu_info=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits 2>/dev/null | head -1)
  if [ -n "$gpu_info" ]; then
    echo "✅ GPU detected: $gpu_info"
    echo "🚀 Hardware acceleration enabled for optimal quality"
  else
    echo "⚠️  GPU detection failed, will use software encoding as fallback"
  fi
else
  echo "⚠️  nvidia-smi not found, will use software encoding as fallback"
fi
echo ""

# ---- Setup ----
mkdir -p "$TEMP_DIR"
CURRENT_DIR=$(pwd)
PROCESSED_FILES=()
CONCAT_LIST="$TEMP_DIR/concat_list.txt"

# Function to download TS segments from m3u8 and concatenate
process_url() {
  local url="$1"
  local index="$2"
  local total_urls="$3"
  local output_file="$TEMP_DIR/video_$index.mp4"
  local ts_dir="$TEMP_DIR/ts_segments_$index"
  local m3u8_file="$TEMP_DIR/playlist_$index.m3u8"

  echo "🎬 Processing video $index/$total_urls..."

  # Function to handle errors and exit gracefully
  handle_error() {
    echo "❌ FATAL ERROR in process $index: $1"
    echo "❌ Process $index failed at: $(date)"
    exit 1
  }

  echo "  📥 Downloading m3u8 playlist..."
  echo "     URL length: ${#url} characters"
  echo "     URL preview: $(printf '%.80s' "$url")..."

  # Create directory for TS segments
  mkdir -p "$ts_dir"

  # Download the m3u8 playlist
  if ! curl -s -L "$url" -o "$m3u8_file"; then
    handle_error "Failed to download m3u8 playlist"
  fi

  echo "  � Parsing m3u8 playlist..."

  # Extract base URL from the m3u8 URL
  base_url=$(echo "$url" | sed 's|/[^/]*$|/|')

  # First, check if this is a master playlist or a media playlist
  actual_m3u8_url=""
  ts_urls=()

  while IFS= read -r line; do
    # Skip comments and empty lines
    if [[ "$line" =~ ^#.*$ ]] || [[ -z "$line" ]]; then
      continue
    fi

    # Check if this line looks like a TS segment (.ts) or another m3u8
    if [[ "$line" =~ \.ts(\?.*)?$ ]]; then
      # This is a TS segment - handle as media playlist
      if [[ "$line" =~ ^https?:// ]]; then
        ts_urls+=("$line")
      else
        ts_urls+=("${base_url}${line}")
      fi
    elif [[ "$line" =~ \.m3u8(\?.*)?$ ]]; then
      # This is another m3u8 file - this is a master playlist
      if [[ "$line" =~ ^https?:// ]]; then
        actual_m3u8_url="$line"
      else
        actual_m3u8_url="${base_url}${line}"
      fi
      echo "  🔗 Found master playlist pointing to: $actual_m3u8_url"
      break
    fi
  done < "$m3u8_file"

  # If we found a master playlist, download the actual media playlist
  if [[ -n "$actual_m3u8_url" ]]; then
    echo "  📥 Downloading actual media playlist..."
    media_m3u8_file="$TEMP_DIR/media_playlist_$index.m3u8"

    if ! curl -s -L "$actual_m3u8_url" -o "$media_m3u8_file"; then
      handle_error "Failed to download media playlist from master"
    fi

    # Extract base URL for the media playlist
    media_base_url=$(echo "$actual_m3u8_url" | sed 's|/[^/]*$|/|')

    # Parse the media playlist for TS segments
    echo "  📋 Parsing media playlist for TS segments..."
    ts_urls=()
    while IFS= read -r line; do
      # Skip comments and empty lines
      if [[ "$line" =~ ^#.*$ ]] || [[ -z "$line" ]]; then
        continue
      fi

      # Handle TS segments
      if [[ "$line" =~ \.ts(\?.*)?$ ]]; then
        if [[ "$line" =~ ^https?:// ]]; then
          ts_urls+=("$line")
        else
          ts_urls+=("${media_base_url}${line}")
        fi
      fi
    done < "$media_m3u8_file"

    rm -f "$media_m3u8_file"
  fi

  total_segments=${#ts_urls[@]}
  echo "  📊 Found $total_segments TS segments to download"

  if [ $total_segments -eq 0 ]; then
    handle_error "No TS segments found in m3u8 playlist"
  fi

  # Download TS segments
  echo "  ⬇️  Downloading TS segments..."
  segment_count=0
  for ts_url in "${ts_urls[@]}"; do
    ((segment_count++))
    segment_file="$ts_dir/segment_$(printf "%04d" $segment_count).ts"

    if ! curl -s -L "$ts_url" -o "$segment_file"; then
      echo "  ⚠️  Failed to download segment $segment_count/$total_segments, skipping..."
      continue
    fi

    # Show progress every 50 segments
    if (( segment_count % 50 == 0 )); then
      echo "  📈 Downloaded $segment_count/$total_segments segments..."
    fi
  done

  echo "  ✅ Downloaded $segment_count TS segments"

  # Create concat list for TS segments
  ts_concat_list="$ts_dir/concat_list.txt"
  > "$ts_concat_list"
  for ts_file in "$ts_dir"/segment_*.ts; do
    if [ -f "$ts_file" ]; then
      echo "file '$(basename "$ts_file")'" >> "$ts_concat_list"
    fi
  done

  echo "  🔗 Concatenating TS segments to MP4..."

  # Concatenate TS segments to MP4 (no quality enhancement yet - save for final step)
  if ffmpeg \
    -y \
    -hide_banner \
    -loglevel error \
    -f concat \
    -safe 0 \
    -i "$ts_concat_list" \
    -c copy \
    -avoid_negative_ts make_zero \
    -movflags +faststart \
    "$output_file" 2>/dev/null; then
    echo "  ✅ TS segments concatenated to MP4"
  else
    handle_error "Failed to concatenate TS segments"
  fi

  # Keep TS segments for debugging (will be archived at the end)
  echo "  📦 Keeping TS segments for debugging..."

  echo "  ✅ Download and concatenation completed"
  echo "✅ Completed video $index/$total_urls"
  echo ""
}

# ---- Parse source file for timing parameters and URLs ----
echo "==> Reading timing parameters and URLs from $SOURCE_FILE..."

URLS=()
while IFS= read -r line || [ -n "$line" ]; do
  # Trim whitespace more carefully to preserve special characters
  line=$(printf '%s\n' "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

  # Skip empty lines and comments
  if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
    continue
  fi

  # Parse timing parameters
  if [[ "$line" =~ ^video_start=(.+)$ ]]; then
    VIDEO_START="${BASH_REMATCH[1]}"
    echo "   📅 Video start time: $VIDEO_START"
    continue
  elif [[ "$line" =~ ^trim_start=(.+)$ ]]; then
    TRIM_START="${BASH_REMATCH[1]}"
    echo "   ✂️  Trim start time: $TRIM_START"
    continue
  elif [[ "$line" =~ ^trim_end=(.+)$ ]]; then
    TRIM_END="${BASH_REMATCH[1]}"
    echo "   ✂️  Trim end time: $TRIM_END"
    continue
  fi

  # If it's not a timing parameter, treat it as a URL
  URLS+=("$line")
done < "$SOURCE_FILE"

# ---- Validate timing parameters ----
if [[ -z "$VIDEO_START" || -z "$TRIM_START" || -z "$TRIM_END" ]]; then
  echo "❌ ERROR: Missing timing parameters in $SOURCE_FILE"
  echo "   Required: video_start, trim_start, trim_end"
  exit 1
fi

# ---- Calculate timing values ----
echo "==> Calculating trim timing..."
VIDEO_START_SECONDS=$(time_to_seconds "$VIDEO_START")
TRIM_START_SECONDS=$(time_to_seconds "$TRIM_START")
TRIM_END_SECONDS=$(time_to_seconds "$TRIM_END")

# Calculate how much to trim from the start
TRIM_FROM_START_SECONDS=$((TRIM_START_SECONDS - VIDEO_START_SECONDS))

# Calculate final duration (trim_end - trim_start)
FINAL_DURATION_SECONDS=$((TRIM_END_SECONDS - TRIM_START_SECONDS))

echo "   📊 Video starts at: $VIDEO_START ($(seconds_to_time $VIDEO_START_SECONDS))"
echo "   📊 Trim from start: $(seconds_to_time $TRIM_FROM_START_SECONDS)"
echo "   📊 Final duration: $(seconds_to_time $FINAL_DURATION_SECONDS)"
echo ""

total_urls=${#URLS[@]}
echo "==> Found $total_urls URLs to process"
echo "==> Processing sequentially for reliability"
echo ""

# Show list of videos to process
for i in "${!URLS[@]}"; do
  echo "   $((i+1)). Video $((i+1))/$total_urls"
done
echo ""

# Process URLs sequentially
index=1
for url in "${URLS[@]}"; do
  output_file="$TEMP_DIR/video_$index.mp4"
  if [ $RESUME_MODE -eq 1 ] && [ -f "$output_file" ]; then
    echo "⏩ [RESUME] Skipping download for video $index/$total_urls (already exists: $output_file)"
  else
    echo "🚀 Starting download $index/$total_urls"
    echo "📊 Progress: [$index/$total_urls] $(( (index-1) * 100 / total_urls ))% complete"

    # Show remaining downloads
    if [ $index -lt $total_urls ]; then
      echo "📋 Remaining downloads:"
      for ((i=index; i<total_urls; i++)); do
        echo "   $((i+1)). Video $((i+1))/$total_urls"
      done
    fi
    echo ""

    # Process this URL
    if process_url "$url" "$index" "$total_urls"; then
      echo "✅ Download $index/$total_urls completed successfully"
    else
      echo "❌ Download $index/$total_urls failed"
      echo "❌ ERROR: Failed to process video $index"
      exit 1
    fi

    echo ""
    echo "============================================================================"
  fi
  ((index++))
done

echo "🎉 All downloads completed!"

# ---- Create concatenation list in correct order ----
if [ $RESUME_MODE -eq 1 ] && [ -f "$CONCAT_LIST" ]; then
  echo "⏩ [RESUME] Skipping concat list creation (already exists: $CONCAT_LIST)"
  files_found=$(grep -c "^file '" "$CONCAT_LIST")
else
  echo "📋 Creating concatenation list in correct order..."
  > "$CONCAT_LIST"  # Clear the file
  files_found=0
  for ((i=1; i<=total_urls; i++)); do
    output_file="$TEMP_DIR/video_$i.mp4"
    if [ -f "$output_file" ]; then
      echo "file '$(pwd)/$output_file'" >> "$CONCAT_LIST"
      ((files_found++))
      echo "  ✅ Found video_$i.mp4"
    else
      echo "❌ ERROR: Expected output file not found: $output_file"
      exit 1
    fi
  done
fi

# ---- Validate we have the correct number of files ----
echo "🔍 Validating file count before concatenation..."
echo "   Expected: $total_urls video files"
echo "   Found: $files_found video files"

if [ $files_found -ne $total_urls ]; then
  echo "❌ ERROR: File count mismatch!"
  echo "   Expected $total_urls video files (matching URLs in source.txt)"
  echo "   But found only $files_found video files"
  echo "   Cannot proceed with concatenation - missing video files"
  echo ""
  echo "📋 Contents of concatenation list:"
  cat "$CONCAT_LIST"
  echo ""
  echo "📁 Files in temp directory:"
  ls -la "$TEMP_DIR/" 2>/dev/null || echo "Temp directory not found"
  exit 1
fi

echo "✅ File count validation passed: $files_found/$total_urls files ready"

echo "🎬 Concatenating $files_found videos, trimming, and applying RTX 4090 quality enhancement..."
echo "   ✂️  Trimming from start: $(seconds_to_time $TRIM_FROM_START_SECONDS)"
echo "   ⏱️  Final duration: $(seconds_to_time $FINAL_DURATION_SECONDS)"
echo "   🚀 Applying comprehensive quality enhancement with RTX 4090..."

# First concatenate and trim with stream copy (fast)
temp_concatenated="$TEMP_DIR/temp_concatenated.mp4"
if [ $RESUME_MODE -eq 1 ] && [ -f "$temp_concatenated" ]; then
  echo "⏩ [RESUME] Skipping concatenation/trim (already exists: $temp_concatenated)"
else
  echo "   🔗 Step 1: Concatenating and trimming (stream copy)..."
  if ffmpeg \
    -y \
    -hide_banner \
    -loglevel warning \
    -f concat \
    -safe 0 \
    -i "$CONCAT_LIST" \
    -ss "$TRIM_FROM_START_SECONDS" \
    -t "$FINAL_DURATION_SECONDS" \
    -c copy \
    -avoid_negative_ts make_zero \
    "$temp_concatenated" 2>/dev/null; then
    echo "   ✅ Concatenation and trimming completed"
  else
    echo "❌ ERROR: Failed to concatenate and trim videos"
    exit 1
  fi
fi

# Now apply all quality enhancements with NVENC
if [ $RESUME_MODE -eq 1 ] && [ -f "$FINAL_OUTPUT" ]; then
  echo "⏩ [RESUME] Skipping final enhancement (already exists: $FINAL_OUTPUT)"
else
  echo "   🎨 Step 2: Applying quality enhancements (720p→1080p, 24fps→30fps, etc.)..."
  if ffmpeg \
    -y \
    -hide_banner \
    -loglevel warning \
    -i "$temp_concatenated" \
    -vf "scale=1920x1080:flags=lanczos,unsharp=5:5:1.0:5:5:0.0,eq=contrast=1.1:brightness=0.02:saturation=1.1" \
    -c:v h264_nvenc \
    -preset p1 \
    -profile:v high \
    -level 4.1 \
    -rc vbr \
    -cq 16 \
    -qmin 14 \
    -qmax 18 \
    -b:v 8M \
    -maxrate 12M \
    -bufsize 16M \
    -bf 3 \
    -b_ref_mode middle \
    -spatial_aq 1 \
    -temporal_aq 1 \
    -rc-lookahead 32 \
    -surfaces 64 \
    -c:a aac \
    -b:a 256k \
    -ar 48000 \
    -r 30 \
    -movflags +faststart \
    "$FINAL_OUTPUT" 2>/dev/null; then
    echo "✅ Final enhanced output created with RTX 4090 acceleration"
  elif ffmpeg \
    -y \
    -hide_banner \
    -loglevel warning \
    -i "$temp_concatenated" \
    -vf "scale=1920x1080:flags=lanczos" \
    -c:v h264_nvenc \
    -preset p4 \
    -rc vbr \
    -cq 20 \
    -b:v 6M \
    -maxrate 8M \
    -bufsize 12M \
    -c:a aac \
    -b:a 192k \
    -r 30 \
    -movflags +faststart \
    "$FINAL_OUTPUT" 2>/dev/null; then
    echo "✅ Final output created with NVENC fallback mode"
  else
    echo "❌ ERROR: Failed to apply quality enhancements"
    exit 1
  fi
fi

# Archive temp directory instead of deleting
if [ -f "$FINAL_OUTPUT" ]; then
  # Create timestamp for archive folder name
  timestamp=$(date +"%Y%m%d_%H%M%S")
  archive_dir="TEMP_${timestamp}"

  echo "📦 Archiving temp files to: $archive_dir"
  mv "$TEMP_DIR" "$archive_dir"
  echo "✅ Temp files archived (not deleted) for debugging"
else
  echo "⚠️  Final output not found, keeping temp files as-is for debugging"
fi

# Clean up temporary concatenated file if it exists
rm -f "$temp_concatenated" 2>/dev/null

echo ""
echo "🎉 ALL DONE! 🎉"
echo "📁 Final output: $FINAL_OUTPUT"
echo "📊 Successfully processed $(($index - 1)) video(s) from $SOURCE_FILE"
echo "🎥 Output: Enhanced, concatenated and trimmed MP4 file"
echo "🚀 Quality improvements applied:"
echo "   - Resolution: 720p → 1080p (upscaled with Lanczos)"
echo "   - Frame rate: 24fps → 30fps (smoother playback)"
echo "   - Bitrate: Low → 8Mbps (much higher quality)"
echo "   - Sharpening: Applied unsharp mask for clarity"
echo "   - Color: Enhanced contrast, brightness, saturation"
echo "   - Audio: Enhanced to 256kbps AAC"
echo "⏱️  Timing applied:"
echo "   - Original video start: $VIDEO_START"
echo "   - Trimmed from: $TRIM_START"
echo "   - Trimmed to: $TRIM_END"
echo "   - Final duration: $(seconds_to_time $FINAL_DURATION_SECONDS)"
